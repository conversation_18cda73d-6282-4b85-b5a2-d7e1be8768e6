// 登录页面功能
class LoginManager {
    constructor() {
        this.config = window.AppConfig || {};
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadSavedSettings();
        this.checkConnection();
    }

    bindEvents() {
        // 表单提交
        const loginForm = document.getElementById('loginForm');
        loginForm.addEventListener('submit', (e) => this.handleLogin(e));

        // 密码显示/隐藏
        const passwordToggle = document.getElementById('passwordToggle');
        passwordToggle.addEventListener('click', () => this.togglePassword());

        // 服务器地址变化时检查连接
        const serverUrl = document.getElementById('serverUrl');
        serverUrl.addEventListener('blur', () => this.checkConnection());

        // 回车键登录
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const loginBtn = document.getElementById('loginBtn');
                if (!loginBtn.disabled) {
                    loginForm.dispatchEvent(new Event('submit'));
                }
            }
        });
    }

    loadSavedSettings() {
        try {
            const savedSettings = localStorage.getItem('fileShareSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                
                if (settings.serverUrl) {
                    document.getElementById('serverUrl').value = settings.serverUrl;
                }
                
                if (settings.rememberMe && settings.username) {
                    document.getElementById('username').value = settings.username;
                    document.getElementById('rememberMe').checked = true;
                }
                
                if (settings.autoConnect) {
                    document.getElementById('autoConnect').checked = true;
                }
            }
        } catch (error) {
            console.warn('加载保存的设置失败:', error);
        }

        // 设置默认服务器地址
        const serverUrlInput = document.getElementById('serverUrl');
        if (!serverUrlInput.value) {
            serverUrlInput.value = this.config.defaultServerUrl || 'http://localhost:8081';
        }
    }

    saveSettings() {
        try {
            const rememberMe = document.getElementById('rememberMe').checked;
            const autoConnect = document.getElementById('autoConnect').checked;
            const serverUrl = document.getElementById('serverUrl').value;
            const username = document.getElementById('username').value;

            const settings = {
                serverUrl: serverUrl,
                autoConnect: autoConnect
            };

            if (rememberMe) {
                settings.rememberMe = true;
                settings.username = username;
            }

            localStorage.setItem('fileShareSettings', JSON.stringify(settings));
        } catch (error) {
            console.warn('保存设置失败:', error);
        }
    }

    async checkConnection() {
        const serverUrl = document.getElementById('serverUrl').value;
        const statusIcon = document.querySelector('.status-icon');
        const statusText = document.querySelector('.status-text');

        if (!serverUrl) {
            this.updateConnectionStatus(false, '请输入服务器地址');
            return;
        }

        try {
            statusText.textContent = '检查连接中...';
            statusIcon.className = 'fas fa-circle status-icon';

            const response = await fetch(`${serverUrl}/api/health`, {
                method: 'GET',
                timeout: 5000
            });

            if (response.ok) {
                this.updateConnectionStatus(true, '服务器连接正常');
            } else {
                this.updateConnectionStatus(false, '服务器响应异常');
            }
        } catch (error) {
            this.updateConnectionStatus(false, '无法连接到服务器');
        }
    }

    updateConnectionStatus(connected, message) {
        const statusIcon = document.querySelector('.status-icon');
        const statusText = document.querySelector('.status-text');

        statusIcon.className = `fas fa-circle status-icon ${connected ? 'connected' : ''}`;
        statusText.textContent = message;
    }

    togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('.password-toggle i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }

    async handleLogin(event) {
        event.preventDefault();

        const loginBtn = document.getElementById('loginBtn');
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');

        // 获取表单数据
        const formData = new FormData(event.target);
        const loginData = {
            username: formData.get('username'),
            password: formData.get('password'),
            serverUrl: formData.get('serverUrl')
        };

        // 验证输入
        if (!this.validateInput(loginData)) {
            return;
        }

        try {
            // 显示加载状态
            this.setLoadingState(true);

            // 发送登录请求
            const response = await fetch(`${loginData.serverUrl}/api/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: loginData.username,
                    password: loginData.password
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                // 登录成功
                this.showMessage('登录成功，正在跳转...', 'success');

                // 保存设置
                this.saveSettings();

                // 保存认证信息 - 修复数据结构
                const authData = {
                    token: result.token,
                    user: result.user
                };
                this.saveAuthInfo(authData, loginData.serverUrl);

                // 跳转到主页面
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);

            } else {
                // 登录失败
                this.showMessage(result.error || result.message || '登录失败，请检查用户名和密码', 'error');
            }

        } catch (error) {
            console.error('登录错误:', error);
            this.showMessage('网络错误，请检查服务器连接', 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    validateInput(data) {
        if (!data.username.trim()) {
            this.showMessage('请输入用户名', 'error');
            document.getElementById('username').focus();
            return false;
        }

        if (!data.password.trim()) {
            this.showMessage('请输入密码', 'error');
            document.getElementById('password').focus();
            return false;
        }

        if (!data.serverUrl.trim()) {
            this.showMessage('请输入服务器地址', 'error');
            document.getElementById('serverUrl').focus();
            return false;
        }

        // 验证服务器地址格式
        try {
            new URL(data.serverUrl);
        } catch {
            this.showMessage('服务器地址格式不正确', 'error');
            document.getElementById('serverUrl').focus();
            return false;
        }

        return true;
    }

    setLoadingState(loading) {
        const loginBtn = document.getElementById('loginBtn');
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');

        loginBtn.disabled = loading;
        
        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'block';
        } else {
            btnText.style.display = 'block';
            btnLoading.style.display = 'none';
        }
    }

    showMessage(message, type) {
        const errorMsg = document.getElementById('errorMessage');
        const successMsg = document.getElementById('successMessage');

        // 隐藏所有消息
        errorMsg.style.display = 'none';
        successMsg.style.display = 'none';

        // 显示对应类型的消息
        if (type === 'error') {
            errorMsg.textContent = message;
            errorMsg.style.display = 'block';
        } else if (type === 'success') {
            successMsg.textContent = message;
            successMsg.style.display = 'block';
        }

        // 3秒后自动隐藏
        setTimeout(() => {
            errorMsg.style.display = 'none';
            successMsg.style.display = 'none';
        }, 3000);
    }

    saveAuthInfo(authData, serverUrl) {
        try {
            const authInfo = {
                token: authData.token,
                user: authData.user,
                serverUrl: serverUrl,
                loginTime: Date.now()
            };

            localStorage.setItem('fileShareAuth', JSON.stringify(authInfo));
            sessionStorage.setItem('currentServerUrl', serverUrl);
        } catch (error) {
            console.warn('保存认证信息失败:', error);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();
});
