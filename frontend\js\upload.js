/**
 * 文件上传模块
 * 处理文件上传、进度显示、拖拽上传等功能
 */

class FileUploader {
    constructor() {
        this.uploadQueue = [];
        this.activeUploads = new Map();
        this.maxConcurrentUploads = CONFIG.UI.UPLOAD.CONCURRENT_UPLOADS;
        
        this.init();
    }
    
    /**
     * 初始化上传器
     */
    init() {
        this.bindEvents();
        this.setupDragAndDrop();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 上传按钮
        const uploadBtn = Utils.dom.$('#upload-btn');
        if (uploadBtn) {
            Utils.event.on(uploadBtn, 'click', () => {
                Components.Modal.show('upload-modal');
            });
        }
        
        // 文件选择按钮
        const selectFilesBtn = Utils.dom.$('#select-files-btn');
        const fileInput = Utils.dom.$('#file-input');
        
        if (selectFilesBtn && fileInput) {
            Utils.event.on(selectFilesBtn, 'click', () => {
                fileInput.click();
            });
            
            Utils.event.on(fileInput, 'change', (e) => {
                this.handleFileSelect(e.target.files);
            });
        }
        
        // 上传区域点击
        const uploadArea = Utils.dom.$('#upload-area');
        if (uploadArea) {
            Utils.event.on(uploadArea, 'click', () => {
                if (fileInput) fileInput.click();
            });
        }
    }
    
    /**
     * 设置拖拽上传
     */
    setupDragAndDrop() {
        const uploadArea = Utils.dom.$('#upload-area');
        if (!uploadArea) return;
        
        // 阻止默认拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            Utils.event.on(uploadArea, eventName, (e) => {
                Utils.event.preventDefault(e);
                Utils.event.stopPropagation(e);
            });
        });
        
        // 拖拽进入
        Utils.event.on(uploadArea, 'dragenter', () => {
            Utils.dom.addClass(uploadArea, 'dragover');
        });
        
        // 拖拽离开
        Utils.event.on(uploadArea, 'dragleave', (e) => {
            if (!uploadArea.contains(e.relatedTarget)) {
                Utils.dom.removeClass(uploadArea, 'dragover');
            }
        });
        
        // 文件放置
        Utils.event.on(uploadArea, 'drop', (e) => {
            Utils.dom.removeClass(uploadArea, 'dragover');
            const files = e.dataTransfer.files;
            this.handleFileSelect(files);
        });
        
        // 全局拖拽处理
        Utils.event.on(document, 'dragover', (e) => {
            Utils.event.preventDefault(e);
        });
        
        Utils.event.on(document, 'drop', (e) => {
            Utils.event.preventDefault(e);
        });
    }
    
    /**
     * 处理文件选择
     */
    handleFileSelect(files) {
        if (!files || files.length === 0) return;
        
        const fileArray = Array.from(files);
        const validFiles = this.validateFiles(fileArray);
        
        if (validFiles.length > 0) {
            this.addToQueue(validFiles);
            this.showUploadProgress();
            this.startUploads();
        }
    }
    
    /**
     * 验证文件
     */
    validateFiles(files) {
        const validFiles = [];
        const errors = [];
        
        for (const file of files) {
            // 检查文件大小
            if (!Utils.validate.fileSize(file.size)) {
                errors.push(`文件 "${file.name}" 超出大小限制 (${Utils.formatFileSize(CONFIG.FILES.MAX_SIZE)})`);
                continue;
            }
            
            // 检查文件类型
            const allExtensions = Object.values(CONFIG.FILES.SUPPORTED_TYPES).flat();
            if (!Utils.validate.fileType(file.name, allExtensions)) {
                errors.push(`文件 "${file.name}" 类型不支持`);
                continue;
            }
            
            validFiles.push(file);
        }
        
        // 显示错误信息
        if (errors.length > 0) {
            Components.Toast.error(errors.join('\n'));
        }
        
        // 检查文件数量限制
        if (validFiles.length > CONFIG.FILES.MAX_FILES) {
            Components.Toast.warning(`一次最多只能上传 ${CONFIG.FILES.MAX_FILES} 个文件`);
            return validFiles.slice(0, CONFIG.FILES.MAX_FILES);
        }
        
        return validFiles;
    }
    
    /**
     * 添加到上传队列
     */
    addToQueue(files) {
        files.forEach(file => {
            const uploadItem = {
                id: Utils.generateId('upload'),
                file: file,
                progress: 0,
                status: 'pending', // pending, uploading, completed, error
                error: null
            };
            
            this.uploadQueue.push(uploadItem);
        });
    }
    
    /**
     * 显示上传进度
     */
    showUploadProgress() {
        const progressContainer = Utils.dom.$('#upload-progress');
        if (!progressContainer) return;
        
        progressContainer.innerHTML = '';
        
        this.uploadQueue.forEach(item => {
            const progressItem = this.createProgressItem(item);
            progressContainer.appendChild(progressItem);
        });
    }
    
    /**
     * 创建进度项
     */
    createProgressItem(uploadItem) {
        const statusText = this.getStatusText(uploadItem.status);
        const statusClass = this.getStatusClass(uploadItem.status);
        
        return Utils.dom.create('div', {
            className: `progress-item ${statusClass}`,
            'data-upload-id': uploadItem.id,
            innerHTML: `
                <div class="progress-info">
                    <div class="progress-name">${uploadItem.file.name}</div>
                    <div class="progress-status">${statusText}</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${uploadItem.progress}%"></div>
                </div>
                <div class="progress-actions">
                    ${uploadItem.status === 'uploading' ? 
                        '<button class="btn btn-sm btn-secondary cancel-btn">取消</button>' :
                        uploadItem.status === 'error' ?
                        '<button class="btn btn-sm btn-primary retry-btn">重试</button>' :
                        ''
                    }
                </div>
            `
        });
    }
    
    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            pending: '等待上传',
            uploading: '上传中',
            completed: '上传完成',
            error: '上传失败'
        };
        return statusMap[status] || '未知状态';
    }
    
    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
        const classMap = {
            pending: 'status-pending',
            uploading: 'status-uploading',
            completed: 'status-completed',
            error: 'status-error'
        };
        return classMap[status] || '';
    }
    
    /**
     * 开始上传
     */
    async startUploads() {
        while (this.uploadQueue.length > 0 && this.activeUploads.size < this.maxConcurrentUploads) {
            const uploadItem = this.uploadQueue.shift();
            this.uploadFile(uploadItem);
        }
    }
    
    /**
     * 上传单个文件
     */
    async uploadFile(uploadItem) {
        try {
            this.activeUploads.set(uploadItem.id, uploadItem);
            uploadItem.status = 'uploading';
            this.updateProgressItem(uploadItem);
            
            const formData = new FormData();
            formData.append('file', uploadItem.file);
            
            // 如果在文件夹中，添加文件夹ID
            if (fileManager && fileManager.currentFolder) {
                formData.append('folder_id', fileManager.currentFolder.id);
            }
            
            const response = await this.uploadWithProgress(formData, uploadItem);
            
            uploadItem.status = 'completed';
            uploadItem.progress = 100;
            this.updateProgressItem(uploadItem);
            
            Components.Toast.success(`文件 "${uploadItem.file.name}" 上传成功`);
            
            // 刷新文件列表
            if (fileManager) {
                fileManager.refresh();
            }
            
        } catch (error) {
            uploadItem.status = 'error';
            uploadItem.error = error.message;
            this.updateProgressItem(uploadItem);
            
            Components.Toast.error(`文件 "${uploadItem.file.name}" 上传失败: ${error.message}`);
        } finally {
            this.activeUploads.delete(uploadItem.id);
            
            // 继续处理队列
            if (this.uploadQueue.length > 0) {
                this.startUploads();
            }
        }
    }
    
    /**
     * 带进度的上传
     */
    uploadWithProgress(formData, uploadItem) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            // 上传进度
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    uploadItem.progress = Math.round((e.loaded / e.total) * 100);
                    this.updateProgressItem(uploadItem);
                }
            });
            
            // 完成
            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        reject(new Error('响应解析失败'));
                    }
                } else {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            });
            
            // 错误
            xhr.addEventListener('error', () => {
                reject(new Error('网络错误'));
            });
            
            // 超时
            xhr.addEventListener('timeout', () => {
                reject(new Error('上传超时'));
            });
            
            // 取消
            uploadItem.xhr = xhr;
            
            xhr.timeout = CONFIG.API.TIMEOUT;
            xhr.open('POST', `${CONFIG.API.BASE_URL}${CONFIG.API.ENDPOINTS.UPLOAD}`);
            xhr.send(formData);
        });
    }
    
    /**
     * 更新进度项
     */
    updateProgressItem(uploadItem) {
        const progressItem = Utils.dom.$(`[data-upload-id="${uploadItem.id}"]`);
        if (!progressItem) return;
        
        const progressFill = progressItem.querySelector('.progress-fill');
        const progressStatus = progressItem.querySelector('.progress-status');
        const progressActions = progressItem.querySelector('.progress-actions');
        
        if (progressFill) {
            progressFill.style.width = uploadItem.progress + '%';
        }
        
        if (progressStatus) {
            progressStatus.textContent = this.getStatusText(uploadItem.status);
        }
        
        if (progressActions) {
            progressActions.innerHTML = this.getActionButtons(uploadItem);
        }
        
        // 更新样式类
        progressItem.className = `progress-item ${this.getStatusClass(uploadItem.status)}`;
    }
    
    /**
     * 获取操作按钮
     */
    getActionButtons(uploadItem) {
        switch (uploadItem.status) {
            case 'uploading':
                return '<button class="btn btn-sm btn-secondary cancel-btn" onclick="fileUploader.cancelUpload(\'' + uploadItem.id + '\')">取消</button>';
            case 'error':
                return '<button class="btn btn-sm btn-primary retry-btn" onclick="fileUploader.retryUpload(\'' + uploadItem.id + '\')">重试</button>';
            default:
                return '';
        }
    }
    
    /**
     * 取消上传
     */
    cancelUpload(uploadId) {
        const uploadItem = this.activeUploads.get(uploadId);
        if (uploadItem && uploadItem.xhr) {
            uploadItem.xhr.abort();
            uploadItem.status = 'error';
            uploadItem.error = '用户取消';
            this.updateProgressItem(uploadItem);
            this.activeUploads.delete(uploadId);
        }
    }
    
    /**
     * 重试上传
     */
    retryUpload(uploadId) {
        // 从已完成的项目中找到要重试的项目
        const progressItem = Utils.dom.$(`[data-upload-id="${uploadId}"]`);
        if (!progressItem) return;
        
        // 重置状态
        const uploadItem = {
            id: uploadId,
            file: null, // 需要重新获取文件
            progress: 0,
            status: 'pending',
            error: null
        };
        
        // 重新添加到队列
        this.uploadQueue.unshift(uploadItem);
        this.startUploads();
    }
    
    /**
     * 清除已完成的上传
     */
    clearCompleted() {
        const completedItems = Utils.dom.$$('.progress-item.status-completed');
        completedItems.forEach(item => {
            item.remove();
        });
    }
    
    /**
     * 清除所有上传
     */
    clearAll() {
        // 取消所有活动上传
        this.activeUploads.forEach(uploadItem => {
            if (uploadItem.xhr) {
                uploadItem.xhr.abort();
            }
        });
        
        // 清空队列和活动上传
        this.uploadQueue = [];
        this.activeUploads.clear();
        
        // 清空UI
        const progressContainer = Utils.dom.$('#upload-progress');
        if (progressContainer) {
            progressContainer.innerHTML = '';
        }
    }
}

// 创建全局上传器实例
let fileUploader;

document.addEventListener('DOMContentLoaded', () => {
    fileUploader = new FileUploader();
});

// 全局可用
window.FileUploader = FileUploader;
window.fileUploader = null;
