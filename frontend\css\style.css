/* 全局样式 */
:root {
    /* 主色调 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
    
    /* 辅助色 */
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* 中性色 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-hover: #f1f5f9;

    /* 文本颜色 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    
    /* 边框 */
    --border-color: #e2e8f0;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* 字体 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* 间距 */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    
    /* 过渡 */
    --transition: all 0.2s ease-in-out;
    --transition-fast: all 0.15s ease-in-out;
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-secondary);
    color: var(--gray-800);
    overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 4px;
    transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 通用类 */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-200);
}

.btn-ghost {
    background-color: transparent;
    color: var(--gray-600);
}

.btn-ghost:hover:not(:disabled) {
    background-color: var(--gray-100);
    color: var(--gray-800);
}

.btn-sm {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
}

/* 输入框样式 */
.input {
    width: 100%;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-primary);
    transition: var(--transition);
}

.input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.input::placeholder {
    color: var(--gray-400);
}

/* 卡片样式 */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.card-body {
    padding: var(--spacing-6);
}

.card-footer {
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease-in-out;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 导航栏 */
.navbar {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    width: 100%;
    padding: 0 var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    gap: var(--spacing-4);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    flex-shrink: 0;
    min-width: 200px;
}

.nav-brand i {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

.nav-search {
    flex: 1;
    max-width: 450px;
    margin: 0 var(--spacing-4);
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 28px;
    transition: all 0.2s ease;
    overflow: hidden;
    height: 44px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-container:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.search-container:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color-alpha), 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

.search-container .fa-search {
    position: absolute;
    left: 18px;
    color: var(--gray-500);
    font-size: 16px;
    z-index: 2;
    transition: color 0.2s ease;
}

.search-container:focus-within .fa-search {
    color: var(--primary-color);
}

.search-container input {
    width: 100%;
    padding: 12px 56px 12px 48px;
    font-size: 15px;
    font-weight: 400;
    border: none;
    background: transparent;
    color: var(--gray-800);
    outline: none;
    height: 20px;
    line-height: 20px;
}

.search-container input::placeholder {
    color: var(--gray-500);
    font-weight: 400;
}

.search-type-indicator {
    position: absolute;
    right: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: 50%;
    color: white;
    font-size: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.search-container:focus-within .search-type-indicator {
    transform: scale(1.05);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.search-type-indicator .fa-image {
    font-size: 12px;
}

/* 搜索框响应式设计 */
@media (max-width: 768px) {
    .nav-search {
        max-width: 300px;
        margin: 0 var(--spacing-2);
    }

    .search-container {
        height: 40px;
        border-radius: 24px;
    }

    .search-container input {
        padding: 10px 48px 10px 40px;
        font-size: 14px;
    }

    .search-container .fa-search {
        left: 14px;
        font-size: 14px;
    }

    .search-type-indicator {
        right: 12px;
        width: 24px;
        height: 24px;
        font-size: 10px;
    }

    .search-type-indicator .fa-image {
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .nav-search {
        max-width: 250px;
        margin: 0 var(--spacing-1);
    }

    .search-container input::placeholder {
        content: "搜索...";
    }
}



.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    flex-shrink: 0;
    min-width: 220px;
    justify-content: flex-end;
}

.nav-btn {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--gray-700);
    cursor: pointer;
    transition: var(--transition);
}

.nav-btn:hover {
    background-color: var(--gray-100);
}

.nav-btn i {
    font-size: var(--font-size-sm);
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background-color: var(--error-color);
    color: white;
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.user-avatar:hover {
    background-color: var(--primary-hover);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--spacing-2);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-info {
    padding: var(--spacing-4);
}

.user-name {
    font-weight: 600;
    color: var(--gray-800);
}

.user-role {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--gray-100);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    min-height: calc(100vh - 64px);
}

/* 侧边栏 */
.sidebar {
    width: 280px;
    background-color: var(--bg-primary);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-6);
    overflow-y: auto;
    flex-shrink: 0;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: var(--spacing-6);
    overflow-y: auto;
    background-color: var(--bg-secondary);
}

.sidebar-section {
    margin-bottom: var(--spacing-8);
}

.sidebar-section h3 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-4);
}

.sidebar-menu {
    list-style: none;
}

.menu-item {
    margin-bottom: var(--spacing-1);
}

.menu-item a {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.menu-item a:hover {
    background-color: var(--gray-100);
}

.menu-item.active a {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.menu-item i {
    width: 16px;
    text-align: center;
}

.folder-list {
    list-style: none;
}

.folder-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--gray-700);
    cursor: pointer;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.folder-item:hover {
    background-color: var(--gray-100);
}

.folder-item i {
    color: var(--warning-color);
}

.storage-info {
    padding: var(--spacing-4);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.storage-bar {
    width: 100%;
    height: 8px;
    background-color: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-2);
}

.storage-used {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    transition: width 0.3s ease;
}

.storage-text {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

/* 系统状态样式已移除 */

/* 用户菜单样式 */
.user-menu {
    position: relative;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown .user-info {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.user-dropdown .user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.user-dropdown .user-role {
    font-size: 12px;
    color: var(--text-secondary);
}

.user-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.user-dropdown .dropdown-item:hover {
    background: var(--bg-hover);
}

.user-dropdown .dropdown-item i {
    width: 16px;
    text-align: center;
    color: var(--text-secondary);
}

/* 通知面板样式 */
.notification-panel {
    position: fixed;
    top: 60px;
    right: 20px;
    width: 350px;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.notification-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.close-panel {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 4px;
}

.close-panel:hover {
    color: var(--text-primary);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-content.large {
    width: 80vw;
    height: 80vh;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

/* Toast通知样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    max-width: 400px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--error-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px 8px;
}

.toast-title {
    font-weight: 600;
    font-size: 14px;
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 2px;
}

.toast-message {
    padding: 0 16px 12px;
    font-size: 14px;
    color: var(--text-secondary);
}

/* 右键菜单样式 */
.context-menu {
    position: fixed;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.95);
    transition: all 0.2s ease;
}

.context-menu.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.context-menu ul {
    list-style: none;
    margin: 0;
    padding: 4px 0;
}

.context-menu li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
}

.context-menu li:hover {
    background: var(--bg-hover);
}

.context-menu li.divider {
    height: 1px;
    background: var(--border-color);
    margin: 4px 0;
    padding: 0;
}

.context-menu li i {
    width: 16px;
    text-align: center;
    color: var(--text-secondary);
}

/* 错误页面样式 */
.error-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: var(--bg-secondary);
}

.error-content {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 500px;
}

.error-content i {
    font-size: 64px;
    color: var(--error-color);
    margin-bottom: 20px;
}

.error-content h1 {
    font-size: 24px;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.error-content p {
    color: var(--text-secondary);
    margin-bottom: 24px;
    line-height: 1.6;
}

/* 文件缩略图样式 */
.file-thumbnail {
    position: relative;
    width: 100%;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.file-thumbnail:hover img {
    transform: scale(1.05);
}

.file-icon-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 48px;
    color: var(--text-secondary);
}

/* 文件网格项样式优化 */
.file-item {
    position: relative;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
}

.file-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.file-item.selected {
    border-color: var(--primary-color);
    background: var(--primary-color-light);
    box-shadow: 0 0 0 2px var(--primary-color-alpha);
}

.file-item .file-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 120px;
    margin-bottom: 12px;
    font-size: 48px;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    border-radius: 8px;
}

.file-item .file-name {
    font-weight: 500;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
}

/* 移除重复的 file-meta 样式，使用 components.css 中的定义 */

.file-item .file-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.file-item:hover .file-actions {
    opacity: 1;
}

.file-item .action-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
    font-size: 14px;
}

.file-item .action-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 文件列表样式 */
.file-list-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-list-table th {
    background: var(--bg-secondary);
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

.file-list-table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.file-list-table tr:hover {
    background: var(--bg-hover);
}

.file-list-table tr.selected {
    background: var(--primary-color-light);
}

.file-name-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-name-cell .file-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--text-secondary);
}

/* 预览模态框样式 */
.preview-modal .modal-content {
    max-width: 90vw;
    max-height: 90vh;
}

.preview-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: var(--bg-secondary);
    border-radius: 8px;
    overflow: hidden;
}

.preview-container img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
}

.preview-container video {
    max-width: 100%;
    max-height: 70vh;
}

.preview-placeholder {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
}

.preview-placeholder i {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.preview-placeholder p {
    margin-bottom: 20px;
    font-size: 16px;
}

/* 加载动画 */
.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式导航栏 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--spacing-4);
    }

    .nav-brand {
        min-width: 120px;
        font-size: var(--font-size-base);
    }

    .nav-search {
        max-width: 300px;
        margin: 0 var(--spacing-4);
    }

    .nav-actions {
        min-width: 150px;
        gap: var(--spacing-2);
    }

    .nav-btn span {
        display: none;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 var(--spacing-2);
    }

    .nav-brand span {
        display: none;
    }

    .nav-search {
        max-width: 200px;
        margin: 0 var(--spacing-2);
    }

    .search-filters {
        display: none;
    }
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb-nav {
    display: flex;
    align-items: center;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--text-secondary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color 0.2s ease;
}

.breadcrumb-item:hover {
    color: var(--primary-color);
}

.breadcrumb-item i {
    font-size: 14px;
}

/* 视图控制 */
.view-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.layout-toggle {
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 2px;
    gap: 2px;
    margin-right: var(--spacing-3);
}

.layout-btn {
    padding: var(--spacing-2) var(--spacing-3);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: calc(var(--border-radius) - 2px);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
}

.layout-btn:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.layout-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.view-toggle {
    display: flex;
    background: var(--bg-primary);
    border-radius: 6px;
    padding: 2px;
}

.view-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.view-btn.active,
.view-btn:hover {
    background: var(--primary-color);
    color: white;
}

.sort-controls select {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    cursor: pointer;
}

/* 文件网格 - 基础样式 */
.file-grid {
    display: grid;
    gap: var(--spacing-4);
    padding: var(--spacing-4) 0;
}

/* 超大图标视图 */
.file-grid.extra-large-icons {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-6);
}

.file-grid.extra-large-icons .file-item {
    padding: var(--spacing-6);
    border-radius: 20px;
    min-height: 280px;
}

.file-grid.extra-large-icons .file-item .file-icon {
    width: 200px;
    height: 200px;
    border-radius: 16px;
    margin-bottom: var(--spacing-4);
}

.file-grid.extra-large-icons .file-item .file-icon i {
    font-size: 5rem;
}

.file-grid.extra-large-icons .file-item .file-icon img {
    border-radius: 12px;
}

.file-grid.extra-large-icons .file-item .file-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
}

/* 大图标视图 */
.file-grid.large-icons {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: var(--spacing-5);
}

.file-grid.large-icons .file-item {
    padding: var(--spacing-5);
    border-radius: 18px;
    min-height: 220px;
}

.file-grid.large-icons .file-item .file-icon {
    width: 140px;
    height: 140px;
    border-radius: 14px;
    margin-bottom: var(--spacing-3);
}

.file-grid.large-icons .file-item .file-icon i {
    font-size: 4rem;
}

.file-grid.large-icons .file-item .file-icon img {
    border-radius: 10px;
}

.file-grid.large-icons .file-item .file-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
}

/* 中等图标视图 */
.file-grid.medium-icons {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--spacing-4);
}

.file-grid.medium-icons .file-item {
    padding: var(--spacing-4);
    border-radius: 14px;
    min-height: 180px;
}

.file-grid.medium-icons .file-item .file-icon {
    width: 100px;
    height: 100px;
    border-radius: 12px;
    margin-bottom: var(--spacing-2);
}

.file-grid.medium-icons .file-item .file-icon i {
    font-size: 3rem;
}

.file-grid.medium-icons .file-item .file-icon img {
    border-radius: 8px;
}

/* 小图标视图 */
.file-grid.small-icons {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: var(--spacing-3);
}

.file-grid.small-icons .file-item {
    padding: var(--spacing-3);
    border-radius: 12px;
    min-height: 120px;
}

.file-grid.small-icons .file-item .file-icon {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin-bottom: var(--spacing-1);
}

.file-grid.small-icons .file-item .file-icon i {
    font-size: 2rem;
}

.file-grid.small-icons .file-item .file-icon img {
    border-radius: 6px;
}

.file-grid.small-icons .file-item .file-name {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-1);
}

.file-grid.small-icons .file-item .file-meta {
    display: none;
}

/* 文件夹特殊样式 */
.file-item.folder-item {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 193, 7, 0.1) 100%);
}

.file-item.folder-item:hover {
    border-color: var(--warning-color);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
    transform: translateY(-2px);
}

.file-item.folder-item .file-icon {
    color: var(--warning-color);
}

/* 首页文件夹样式 */
.file-grid:not(.large-icons):not(.medium-icons):not(.small-icons):not(.extra-large-icons) {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: var(--spacing-5);
}

/* 横向文件夹卡片布局 */
.file-grid.horizontal-layout {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    padding: var(--spacing-4) 0;
}

.file-grid.horizontal-layout .file-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-4);
    border-radius: 12px;
    min-height: auto;
    text-align: left;
    background: linear-gradient(135deg, #fff8e1, #ffecb3);
    border: 1px solid #ffc107;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-grid.horizontal-layout .file-item:hover {
    background: linear-gradient(135deg, #fff3c4, #ffe082);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
    border-color: #ff8f00;
    transform: translateY(-2px);
}

.file-grid.horizontal-layout .file-item .file-icon {
    width: 60px;
    height: 60px;
    margin-right: var(--spacing-4);
    margin-bottom: 0;
    flex-shrink: 0;
    background: linear-gradient(145deg, #ffd54f, #ffb300);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.file-grid.horizontal-layout .file-item .file-icon i {
    font-size: 2rem;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.file-grid.horizontal-layout .file-item .file-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0;
    text-align: left;
    flex-grow: 1;
    line-height: 1.4;
}

.file-grid.horizontal-layout .file-item:hover .file-name {
    color: #ff8f00;
}

.file-grid.horizontal-layout .file-item .file-meta {
    display: none;
}

.file-grid:not(.large-icons):not(.medium-icons):not(.small-icons):not(.extra-large-icons) .file-item {
    padding: var(--spacing-5);
    border-radius: 16px;
    min-height: 200px;
}

.file-grid:not(.large-icons):not(.medium-icons):not(.small-icons):not(.extra-large-icons) .file-item .file-icon {
    width: 120px;
    height: 120px;
    border-radius: 14px;
    margin-bottom: var(--spacing-3);
}

.file-grid:not(.large-icons):not(.medium-icons):not(.small-icons):not(.extra-large-icons) .file-item .file-icon i {
    font-size: 3.5rem;
}

.file-grid:not(.large-icons):not(.medium-icons):not(.small-icons):not(.extra-large-icons) .file-item .file-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-1);
}

/* 图片文件缩略图样式 */
.file-item .file-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 面包屑分隔符 */
.breadcrumb-separator {
    margin: 0 8px;
    color: var(--text-secondary);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

/* 移除重复的文件项样式，使用 components.css 中的定义 */

.file-info {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 文件列表 */
.file-list {
    background: var(--bg-primary);
    border-radius: 8px;
    overflow: hidden;
}

.file-table {
    width: 100%;
    border-collapse: collapse;
}

.file-table th,
.file-table td {
    padding: var(--spacing-2) var(--spacing-3);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.file-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    height: 32px;
}

.file-table tr {
    transition: all 0.2s ease;
    height: 32px;
}

.file-table tr:hover {
    background: var(--bg-hover);
}

/* 详情视图简洁横向布局 */
.file-table .file-name-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    min-height: 40px;
    padding: var(--spacing-2) var(--spacing-3);
}

.file-table .file-icon {
    width: 20px;
    height: 20px;
    margin: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-table .file-icon i {
    font-size: 1rem;
    color: var(--gray-600);
}

.file-table .file-name {
    font-size: var(--font-size-sm);
    font-weight: 400;
    color: var(--gray-800);
    flex-grow: 1;
    line-height: 1.2;
}

.file-table td {
    padding: var(--spacing-2) var(--spacing-3);
    vertical-align: middle;
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    height: 32px;
}

.file-table .file-actions {
    display: flex;
    gap: var(--spacing-1);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.file-table tr:hover .file-actions {
    opacity: 1;
}

.file-table .action-btn {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--gray-600);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.file-table .action-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 表格列宽度控制 - 简洁布局 */
.file-table th:nth-child(1),
.file-table td:nth-child(1) {
    width: 50%;
    min-width: 200px;
}

.file-table th:nth-child(2),
.file-table td:nth-child(2) {
    width: 20%;
    min-width: 80px;
    text-align: right;
    padding-right: var(--spacing-4);
}

.file-table th:nth-child(3),
.file-table td:nth-child(3) {
    width: 15%;
    min-width: 80px;
    text-align: center;
}

.file-table th:nth-child(4),
.file-table td:nth-child(4) {
    width: 10%;
    min-width: 60px;
    text-align: center;
}

.file-table th:nth-child(5),
.file-table td:nth-child(5) {
    width: 5%;
    min-width: 40px;
    text-align: center;
}

/* 文件夹横向卡片布局 */
.folder-horizontal-cards {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-4);
    padding: var(--spacing-4) 0;
}

.folder-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-4);
    width: 160px;
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-3);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.folder-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.folder-card.selected {
    border-color: var(--primary-color);
    background: var(--primary-color-light);
    box-shadow: 0 0 0 2px var(--primary-color-alpha);
}

.folder-card .folder-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #ffa726, #ff9800);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.folder-card .folder-icon i {
    font-size: 2rem;
    color: white;
}

.folder-card .folder-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-800);
    line-height: 1.2;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 详情视图卡片布局 */
.file-details-cards {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    padding: var(--spacing-4) 0;
}

.file-detail-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-detail-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.file-detail-card.selected {
    border-color: var(--primary-color);
    background: var(--primary-color-light);
    box-shadow: 0 0 0 2px var(--primary-color-alpha);
}

.file-detail-card .file-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    border-radius: 8px;
    flex-shrink: 0;
}

.file-detail-card .file-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.file-detail-card .file-info {
    flex: 1;
    min-width: 0;
}

.file-detail-card .file-name {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--gray-800);
    margin-bottom: var(--spacing-2);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-detail-card .file-meta {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.file-detail-card .file-meta-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.file-detail-card .file-meta-label {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    font-weight: 500;
}

.file-detail-card .file-meta-value {
    font-size: var(--font-size-sm);
    color: var(--gray-800);
}

.file-detail-card .file-actions {
    display: flex;
    gap: var(--spacing-2);
    flex-shrink: 0;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}
