/**
 * UI组件库
 * 提供可复用的UI组件
 */

const Components = {
    /**
     * Toast通知组件
     */
    Toast: {
        container: null,
        
        init() {
            this.container = Utils.dom.$('#toast-container');
            if (!this.container) {
                this.container = Utils.dom.create('div', {
                    id: 'toast-container',
                    className: 'toast-container'
                });
                document.body.appendChild(this.container);
            }
        },
        
        show(message, type = 'info', duration = CONFIG.UI.NOTIFICATION.DURATION) {
            if (!this.container) this.init();
            
            const toast = Utils.dom.create('div', {
                className: `toast ${type}`,
                innerHTML: `
                    <div class="toast-header">
                        <span class="toast-title">${this.getTypeTitle(type)}</span>
                        <button class="toast-close">&times;</button>
                    </div>
                    <div class="toast-message">${message}</div>
                `
            });
            
            // 添加关闭事件
            const closeBtn = toast.querySelector('.toast-close');
            Utils.event.on(closeBtn, 'click', () => this.hide(toast));
            
            // 添加到容器
            this.container.appendChild(toast);
            
            // 显示动画
            setTimeout(() => Utils.dom.addClass(toast, 'show'), 10);
            
            // 自动隐藏
            if (duration > 0) {
                setTimeout(() => this.hide(toast), duration);
            }
            
            return toast;
        },
        
        hide(toast) {
            Utils.dom.removeClass(toast, 'show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        },
        
        getTypeTitle(type) {
            const titles = {
                success: '成功',
                error: '错误',
                warning: '警告',
                info: '信息'
            };
            return titles[type] || '通知';
        },
        
        success(message, duration) {
            return this.show(message, 'success', duration);
        },
        
        error(message, duration) {
            return this.show(message, 'error', duration);
        },
        
        warning(message, duration) {
            return this.show(message, 'warning', duration);
        },
        
        info(message, duration) {
            return this.show(message, 'info', duration);
        }
    },
    
    /**
     * 模态框组件
     */
    Modal: {
        show(modalId) {
            const modal = Utils.dom.$(`#${modalId}`);
            if (modal) {
                Utils.dom.addClass(modal, 'show');
                Utils.dom.addClass(document.body, 'modal-open');
                
                // 添加ESC键关闭
                const escHandler = (e) => {
                    if (e.key === 'Escape') {
                        this.hide(modalId);
                        Utils.event.off(document, 'keydown', escHandler);
                    }
                };
                Utils.event.on(document, 'keydown', escHandler);
                
                // 添加背景点击关闭
                const clickHandler = (e) => {
                    if (e.target === modal) {
                        this.hide(modalId);
                    }
                };
                Utils.event.on(modal, 'click', clickHandler);
            }
        },
        
        hide(modalId) {
            const modal = Utils.dom.$(`#${modalId}`);
            if (modal) {
                Utils.dom.removeClass(modal, 'show');
                Utils.dom.removeClass(document.body, 'modal-open');
            }
        },
        
        toggle(modalId) {
            const modal = Utils.dom.$(`#${modalId}`);
            if (modal && Utils.dom.hasClass(modal, 'show')) {
                this.hide(modalId);
            } else {
                this.show(modalId);
            }
        }
    },
    
    /**
     * 加载动画组件
     */
    Loading: {
        show(message = '正在加载...') {
            let overlay = Utils.dom.$('#loading-overlay');
            if (!overlay) {
                overlay = Utils.dom.create('div', {
                    id: 'loading-overlay',
                    className: 'loading-overlay',
                    innerHTML: `
                        <div class="loading-spinner">
                            <div class="spinner"></div>
                            <p>${message}</p>
                        </div>
                    `
                });
                document.body.appendChild(overlay);
            } else {
                const text = overlay.querySelector('p');
                if (text) text.textContent = message;
            }
            
            Utils.dom.removeClass(overlay, 'hidden');
            return overlay;
        },
        
        hide() {
            const overlay = Utils.dom.$('#loading-overlay');
            if (overlay) {
                Utils.dom.addClass(overlay, 'hidden');
            }
        }
    },
    
    /**
     * 确认对话框组件
     */
    Confirm: {
        show(message, title = '确认', onConfirm = null, onCancel = null) {
            return new Promise((resolve) => {
                const modal = Utils.dom.create('div', {
                    className: 'modal show',
                    innerHTML: `
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>${title}</h3>
                            </div>
                            <div class="modal-body">
                                <p>${message}</p>
                            </div>
                            <div class="modal-footer">
                                <button class="btn btn-secondary cancel-btn">取消</button>
                                <button class="btn btn-primary confirm-btn">确认</button>
                            </div>
                        </div>
                    `
                });
                
                const confirmBtn = modal.querySelector('.confirm-btn');
                const cancelBtn = modal.querySelector('.cancel-btn');
                
                const cleanup = () => {
                    document.body.removeChild(modal);
                    Utils.dom.removeClass(document.body, 'modal-open');
                };
                
                Utils.event.on(confirmBtn, 'click', () => {
                    cleanup();
                    if (onConfirm) onConfirm();
                    resolve(true);
                });
                
                Utils.event.on(cancelBtn, 'click', () => {
                    cleanup();
                    if (onCancel) onCancel();
                    resolve(false);
                });
                
                document.body.appendChild(modal);
                Utils.dom.addClass(document.body, 'modal-open');
            });
        }
    },
    
    /**
     * 右键菜单组件
     */
    ContextMenu: {
        current: null,
        
        show(x, y, items) {
            this.hide();
            
            const menu = Utils.dom.create('div', {
                className: 'context-menu show',
                style: `left: ${x}px; top: ${y}px;`,
                innerHTML: `
                    <ul>
                        ${items.map(item => {
                            if (item.divider) {
                                return '<li class="divider"></li>';
                            }
                            return `
                                <li data-action="${item.action}">
                                    <i class="${item.icon}"></i>
                                    ${item.text}
                                </li>
                            `;
                        }).join('')}
                    </ul>
                `
            });
            
            // 添加点击事件
            Utils.event.on(menu, 'click', (e) => {
                const item = e.target.closest('li');
                if (item && item.dataset.action) {
                    const action = item.dataset.action;
                    const selectedItem = items.find(i => i.action === action);
                    if (selectedItem && selectedItem.handler) {
                        selectedItem.handler();
                    }
                    this.hide();
                }
            });
            
            document.body.appendChild(menu);
            this.current = menu;
            
            // 调整位置防止超出屏幕
            this.adjustPosition(menu);
            
            // 点击其他地方关闭
            setTimeout(() => {
                Utils.event.on(document, 'click', this.hideHandler.bind(this));
            }, 10);
        },
        
        hide() {
            if (this.current) {
                Utils.event.off(document, 'click', this.hideHandler);
                document.body.removeChild(this.current);
                this.current = null;
            }
        },
        
        hideHandler(e) {
            if (!this.current || !this.current.contains(e.target)) {
                this.hide();
            }
        },
        
        adjustPosition(menu) {
            const rect = menu.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            
            if (rect.right > viewportWidth) {
                menu.style.left = (viewportWidth - rect.width - 10) + 'px';
            }
            
            if (rect.bottom > viewportHeight) {
                menu.style.top = (viewportHeight - rect.height - 10) + 'px';
            }
        }
    },
    
    /**
     * 进度条组件
     */
    ProgressBar: {
        create(container, options = {}) {
            const {
                value = 0,
                max = 100,
                showText = true,
                className = ''
            } = options;
            
            const progressBar = Utils.dom.create('div', {
                className: `progress-bar ${className}`,
                innerHTML: `
                    <div class="progress-fill" style="width: ${(value / max) * 100}%"></div>
                    ${showText ? `<span class="progress-text">${value}%</span>` : ''}
                `
            });
            
            if (container) {
                container.appendChild(progressBar);
            }
            
            return {
                element: progressBar,
                setValue(newValue) {
                    const percentage = Math.min(100, Math.max(0, (newValue / max) * 100));
                    const fill = progressBar.querySelector('.progress-fill');
                    const text = progressBar.querySelector('.progress-text');
                    
                    if (fill) {
                        fill.style.width = percentage + '%';
                    }
                    
                    if (text) {
                        text.textContent = Math.round(newValue) + '%';
                    }
                }
            };
        }
    },
    
    /**
     * 分页组件
     */
    Pagination: {
        create(container, options = {}) {
            const {
                currentPage = 1,
                totalPages = 1,
                onPageChange = null,
                showInfo = true
            } = options;
            
            const pagination = Utils.dom.create('div', {
                className: 'pagination',
                innerHTML: this.generateHTML(currentPage, totalPages, showInfo)
            });
            
            // 添加点击事件
            Utils.event.on(pagination, 'click', (e) => {
                const button = e.target.closest('.page-btn');
                if (button && !Utils.dom.hasClass(button, 'disabled')) {
                    const page = parseInt(button.dataset.page);
                    if (page && onPageChange) {
                        onPageChange(page);
                    }
                }
            });
            
            if (container) {
                container.appendChild(pagination);
            }
            
            return {
                element: pagination,
                update(newCurrentPage, newTotalPages) {
                    pagination.innerHTML = this.generateHTML(newCurrentPage, newTotalPages, showInfo);
                }
            };
        },
        
        generateHTML(currentPage, totalPages, showInfo) {
            let html = '<div class="pagination-buttons">';
            
            // 上一页
            html += `
                <button class="page-btn ${currentPage <= 1 ? 'disabled' : ''}" data-page="${currentPage - 1}">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
            
            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <button class="page-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">
                        ${i}
                    </button>
                `;
            }
            
            // 下一页
            html += `
                <button class="page-btn ${currentPage >= totalPages ? 'disabled' : ''}" data-page="${currentPage + 1}">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
            
            html += '</div>';
            
            // 页面信息
            if (showInfo) {
                html += `
                    <div class="pagination-info">
                        第 ${currentPage} 页，共 ${totalPages} 页
                    </div>
                `;
            }
            
            return html;
        }
    }
};

// 初始化组件
document.addEventListener('DOMContentLoaded', () => {
    // 初始化Toast容器
    Components.Toast.init();
    
    // 初始化模态框关闭按钮
    Utils.dom.$$('.modal-close').forEach(btn => {
        Utils.event.on(btn, 'click', (e) => {
            const modal = e.target.closest('.modal');
            if (modal) {
                Components.Modal.hide(modal.id);
            }
        });
    });
});

// 全局可用
window.Components = Components;
