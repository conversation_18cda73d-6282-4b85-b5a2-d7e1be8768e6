#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载服务 - 处理文件下载和加密
"""

import os
import zipfile
import tempfile
import secrets
import string
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import shutil

from utils.logger import setup_logger
from models.file_share import SharedFile, SharedFolder

class DownloadService:
    """下载服务类"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("DownloadService")
        self.temp_dir = os.path.join(os.getcwd(), "temp", "downloads")
        self.ensure_temp_dir()
    
    def ensure_temp_dir(self):
        """确保临时目录存在"""
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir, exist_ok=True)
    
    def generate_password(self, length: int = 8) -> str:
        """生成随机密码"""
        characters = string.ascii_letters + string.digits
        return ''.join(secrets.choice(characters) for _ in range(length))
    
    def should_encrypt_file(self, file_id: int) -> bool:
        """检查文件是否需要加密"""
        try:
            with self.db_manager.get_session() as session:
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if not file_record:
                    return False
                
                folder = session.query(SharedFolder).filter_by(id=file_record.folder_id).first()
                if not folder:
                    return False
                
                # 获取加密阈值（默认5次）
                encrypt_threshold = 5  # 可以从文件夹权限设置中获取
                
                # 检查下载次数是否超过阈值
                return file_record.download_count >= encrypt_threshold
                
        except Exception as e:
            self.logger.error(f"检查文件加密状态失败: {e}")
            return False
    
    def create_zip_file(self, files: List[Dict[str, Any]], zip_name: str, 
                       password: Optional[str] = None) -> Dict[str, Any]:
        """创建压缩文件"""
        try:
            zip_path = os.path.join(self.temp_dir, f"{zip_name}.zip")
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 如果有密码，设置密码保护
                if password:
                    zipf.setpassword(password.encode('utf-8'))
                
                for file_info in files:
                    file_path = file_info['full_path']
                    archive_name = file_info['archive_name']
                    
                    if os.path.exists(file_path):
                        if password:
                            # 加密压缩
                            zipf.write(file_path, archive_name, compress_type=zipfile.ZIP_DEFLATED)
                        else:
                            # 普通压缩
                            zipf.write(file_path, archive_name)
                    else:
                        self.logger.warning(f"文件不存在: {file_path}")
            
            if os.path.exists(zip_path):
                file_size = os.path.getsize(zip_path)
                return {
                    "success": True,
                    "zip_path": zip_path,
                    "file_size": file_size,
                    "password": password
                }
            else:
                return {"success": False, "error": "压缩文件创建失败"}
                
        except Exception as e:
            self.logger.error(f"创建压缩文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def prepare_single_file_download(self, file_id: int, user_id: int = None) -> Dict[str, Any]:
        """准备单文件下载"""
        try:
            with self.db_manager.get_session() as session:
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if not file_record:
                    return {"success": False, "error": "文件不存在"}
                
                folder = session.query(SharedFolder).filter_by(id=file_record.folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                # 获取文件完整路径
                full_path = os.path.join(folder.path, file_record.relative_path)
                if not os.path.exists(full_path):
                    return {"success": False, "error": "文件不存在于磁盘"}
                
                # 检查是否需要加密
                needs_encryption = self.should_encrypt_file(file_id)
                password = None
                
                if needs_encryption:
                    password = self.generate_password()
                    self.logger.info(f"文件 {file_record.filename} 需要加密下载")
                
                # 准备压缩文件信息
                files_to_zip = [{
                    'full_path': full_path,
                    'archive_name': file_record.filename
                }]
                
                # 生成压缩文件名
                zip_name = f"{file_record.filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # 创建压缩文件
                zip_result = self.create_zip_file(files_to_zip, zip_name, password)
                
                if zip_result.get('success'):
                    # 更新下载次数
                    file_record.increment_download_count()
                    session.commit()
                    
                    # 记录下载信息
                    download_info = {
                        "success": True,
                        "file_id": file_id,
                        "filename": file_record.filename,
                        "zip_path": zip_result['zip_path'],
                        "file_size": zip_result['file_size'],
                        "is_encrypted": needs_encryption,
                        "password": password,
                        "download_count": file_record.download_count
                    }
                    
                    # 如果加密，保存密码申请记录
                    if needs_encryption and password:
                        self.save_password_request(file_id, password, user_id)
                    
                    return download_info
                else:
                    return zip_result
                
        except Exception as e:
            self.logger.error(f"准备文件下载失败: {e}")
            return {"success": False, "error": str(e)}
    
    def prepare_batch_download(self, file_ids: List[int], user_id: int = None) -> Dict[str, Any]:
        """准备批量文件下载"""
        try:
            with self.db_manager.get_session() as session:
                files_to_zip = []
                encrypted_files = []
                total_downloads = 0
                
                for file_id in file_ids:
                    file_record = session.query(SharedFile).filter_by(id=file_id).first()
                    if not file_record:
                        continue
                    
                    folder = session.query(SharedFolder).filter_by(id=file_record.folder_id).first()
                    if not folder:
                        continue
                    
                    full_path = os.path.join(folder.path, file_record.relative_path)
                    if not os.path.exists(full_path):
                        continue
                    
                    files_to_zip.append({
                        'full_path': full_path,
                        'archive_name': f"{folder.name}/{file_record.relative_path}"
                    })
                    
                    # 检查是否有文件需要加密
                    if self.should_encrypt_file(file_id):
                        encrypted_files.append(file_id)
                    
                    # 更新下载次数
                    file_record.increment_download_count()
                    total_downloads += 1
                
                if not files_to_zip:
                    return {"success": False, "error": "没有有效的文件可下载"}
                
                # 如果有加密文件，整个压缩包都加密
                password = None
                if encrypted_files:
                    password = self.generate_password()
                    self.logger.info(f"批量下载包含 {len(encrypted_files)} 个需要加密的文件")
                
                # 生成压缩文件名
                zip_name = f"batch_download_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # 创建压缩文件
                zip_result = self.create_zip_file(files_to_zip, zip_name, password)
                
                if zip_result.get('success'):
                    session.commit()
                    
                    download_info = {
                        "success": True,
                        "file_count": len(files_to_zip),
                        "zip_path": zip_result['zip_path'],
                        "file_size": zip_result['file_size'],
                        "is_encrypted": bool(encrypted_files),
                        "password": password,
                        "encrypted_file_ids": encrypted_files
                    }
                    
                    # 如果加密，保存密码申请记录
                    if password:
                        for file_id in encrypted_files:
                            self.save_password_request(file_id, password, user_id)
                    
                    return download_info
                else:
                    return zip_result
                
        except Exception as e:
            self.logger.error(f"准备批量下载失败: {e}")
            return {"success": False, "error": str(e)}
    
    def save_password_request(self, file_id: int, password: str, user_id: int = None):
        """保存密码申请记录"""
        try:
            # 这里可以保存到数据库中，记录密码申请信息
            # 暂时记录到日志
            self.logger.info(f"保存密码申请记录: 文件ID={file_id}, 用户ID={user_id}")
        except Exception as e:
            self.logger.error(f"保存密码申请记录失败: {e}")
    
    def request_password(self, file_id: int, user_id: int = None) -> Dict[str, Any]:
        """申请解压密码"""
        try:
            # 检查申请次数限制
            max_requests = 3  # 最大申请次数
            current_requests = self.get_password_request_count(file_id, user_id)
            
            if current_requests >= max_requests:
                return {
                    "success": False,
                    "error": f"密码申请次数已达上限 ({max_requests} 次)"
                }
            
            # 获取文件的当前密码
            password = self.get_file_password(file_id)
            if not password:
                return {"success": False, "error": "文件未加密或密码不存在"}
            
            # 记录申请
            self.record_password_request(file_id, user_id)
            
            return {
                "success": True,
                "password": password,
                "remaining_requests": max_requests - current_requests - 1
            }
            
        except Exception as e:
            self.logger.error(f"申请密码失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_password_request_count(self, file_id: int, user_id: int = None) -> int:
        """获取密码申请次数"""
        # 这里应该从数据库查询，暂时返回0
        return 0
    
    def get_file_password(self, file_id: int) -> Optional[str]:
        """获取文件密码"""
        # 这里应该从数据库或缓存中获取密码
        # 暂时返回示例密码
        return "temp123"
    
    def record_password_request(self, file_id: int, user_id: int = None):
        """记录密码申请"""
        try:
            self.logger.info(f"记录密码申请: 文件ID={file_id}, 用户ID={user_id}")
        except Exception as e:
            self.logger.error(f"记录密码申请失败: {e}")
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """清理临时文件"""
        try:
            current_time = datetime.now()
            for filename in os.listdir(self.temp_dir):
                file_path = os.path.join(self.temp_dir, filename)
                if os.path.isfile(file_path):
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if (current_time - file_time).total_seconds() > max_age_hours * 3600:
                        os.remove(file_path)
                        self.logger.info(f"清理临时文件: {filename}")
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")
